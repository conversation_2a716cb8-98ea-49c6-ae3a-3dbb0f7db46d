/* ===== Animation Keyframes ===== */

/* Float Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Fade Up Animation */
@keyframes fadeUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fade Left Animation */
@keyframes fadeLeft {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Fade Right Animation */
@keyframes fadeRight {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scale In Animation */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Pulse Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Rotate Animation */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Slide In Left Animation */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Slide In Right Animation */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Zoom In Animation */
@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Flip In X Animation */
@keyframes flipInX {
    from {
        transform: perspective(400px) rotateX(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateX(-20deg);
    }
    60% {
        transform: perspective(400px) rotateX(10deg);
        opacity: 1;
    }
    80% {
        transform: perspective(400px) rotateX(-5deg);
    }
    to {
        transform: perspective(400px) rotateX(0deg);
        opacity: 1;
    }
}

/* Scroll Bounce Animation */
@keyframes scroll-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Gradient Animation */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

/* Blink Animation */
@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* ===== Animation Classes ===== */

/* Basic Animations */
.animate-fade-up {
    animation: fadeUp 0.8s ease-out forwards;
    opacity: 0;
}

.animate-fade-left {
    animation: fadeLeft 0.8s ease-out forwards;
    opacity: 0;
}

.animate-fade-right {
    animation: fadeRight 0.8s ease-out forwards;
    opacity: 0;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
    opacity: 0;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-rotate {
    animation: rotate 2s linear infinite;
}

.animate-slide-left {
    animation: slideInLeft 0.8s ease-out forwards;
    opacity: 0;
}

.animate-slide-right {
    animation: slideInRight 0.8s ease-out forwards;
    opacity: 0;
}

.animate-zoom-in {
    animation: zoomIn 0.6s ease-out forwards;
    opacity: 0;
}

.animate-flip-x {
    animation: flipInX 0.8s ease-out forwards;
    opacity: 0;
}

/* Animation Delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }
.animate-delay-900 { animation-delay: 0.9s; }
.animate-delay-1000 { animation-delay: 1s; }

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Hover Animations */
.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* Loading Animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: rotate 1s linear infinite;
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* Gradient Background Animation */
.animated-gradient {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

/* Text Animations */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink 0.75s step-end infinite;
}

/* Parallax Effect */
.parallax {
    transform: translateZ(0);
    transition: transform 0.1s ease-out;
}

/* Smooth Reveal Animation */
.reveal {
    position: relative;
    overflow: hidden;
}

.reveal::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: reveal 2s ease-in-out;
}

@keyframes reveal {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Floating Elements */
.floating {
    animation: float 6s ease-in-out infinite;
}

.floating-slow {
    animation: float 8s ease-in-out infinite;
}

.floating-fast {
    animation: float 4s ease-in-out infinite;
}

/* Stagger Animation */
.stagger-animation > * {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeUp 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .parallax {
        transform: none !important;
    }
}

/* ===== WhatsApp Enhanced Animations ===== */

/* WhatsApp Shake Animation */
@keyframes whatsappShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* WhatsApp Glow Animation */
@keyframes whatsappGlow {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    }
    50% {
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.6),
                    0 0 30px rgba(37, 211, 102, 0.4);
    }
}

/* WhatsApp Ring Animation */
@keyframes whatsappRing {
    0% {
        transform: scale(1) rotate(0deg);
    }
    10% {
        transform: scale(1.1) rotate(-5deg);
    }
    20% {
        transform: scale(1.1) rotate(5deg);
    }
    30% {
        transform: scale(1.1) rotate(-5deg);
    }
    40% {
        transform: scale(1.1) rotate(5deg);
    }
    50% {
        transform: scale(1) rotate(0deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
    }
}

/* WhatsApp Heartbeat Animation */
@keyframes whatsappHeartbeat {
    0%, 100% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.1);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.1);
    }
    70% {
        transform: scale(1);
    }
}

/* Enhanced WhatsApp Button Animations */
.whatsapp-float.shake {
    animation: whatsappShake 0.5s ease-in-out;
}

.whatsapp-float.glow .whatsapp-button {
    animation: whatsappGlow 2s ease-in-out infinite;
}

.whatsapp-float.ring .whatsapp-button {
    animation: whatsappRing 1s ease-in-out;
}

.whatsapp-float.heartbeat .whatsapp-button {
    animation: whatsappHeartbeat 1.5s ease-in-out infinite;
}

/* Auto-trigger animations */
.whatsapp-float:nth-child(1) {
    animation-delay: 3s;
}

/* Notification dot animation */
@keyframes notificationPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.whatsapp-notification-dot {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background: #ff4444;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    font-weight: bold;
    animation: notificationPulse 1.5s infinite;
    border: 2px solid white;
}
